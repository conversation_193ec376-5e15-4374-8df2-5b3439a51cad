// Package main demonstrates how to use the integrated metrics system
package main

import (
	"fmt"
	"log"
	"math/rand"
	"time"

	"neuralmetergo/internal/metrics"
)

func main() {
	// Create configuration for the integrated metrics system
	config := metrics.IntegratorConfig{
		CollectionConfig: metrics.CollectionConfig{
			WorkerCount:     2,
			CollectionRate:  100 * time.Millisecond,
			ChannelBuffer:   50,
			BatchSize:       10,
			EnableBatching:  true,
			MaxRetries:      3,
			RetryDelay:      10 * time.Millisecond,
			EnableMetrics:   true,
			ShutdownTimeout: 2 * time.Second,
		},
		AggregationConfig: metrics.AggregationConfig{
			Interval:    500 * time.Millisecond,
			Type:        metrics.AggregationSlidingWindow,
			Percentile:  0.95,
			BufferSize:  100,
			EnableAsync: false, // Synchronous for this example
			WindowConfig: metrics.SlidingWindowConfig{
				Type:        metrics.WindowTypeTime,
				Duration:    30 * time.Second, // 30-second sliding window
				Capacity:    1000,
				EnableStats: true,
				EnableAsync: false,
			},
			BucketConfig: metrics.TimeBucketConfig{
				Granularity:        metrics.GranularitySecond,
				RetentionBuckets:   60, // Keep 60 seconds of data
				MaxClockSkew:       5 * time.Second,
				EnableDownsampling: false,
				EnableUpsampling:   false,
				CleanupInterval:    10 * time.Second,
				EnableAsync:        false,
				EnableCompression:  false,
				MaxMemoryUsage:     10 * 1024 * 1024, // 10MB
				FlushOnShutdown:    true,
			},
			EnableAdvancedStats: true,
		},
		EnableEnhanced:   true,
		EnableCollection: true,
		EnableBasicAgg:   true,
	}

	// Create the integrated metrics system
	integrator, err := metrics.NewMetricsIntegrator(config)
	if err != nil {
		log.Fatalf("Failed to create metrics integrator: %v", err)
	}

	// Start the system
	if err := integrator.Start(); err != nil {
		log.Fatalf("Failed to start metrics integrator: %v", err)
	}
	defer integrator.Stop()

	fmt.Println("🚀 Metrics Integration Example Started")
	fmt.Println("=====================================")

	// Create some metrics for demonstration
	counter := metrics.NewCounter()
	gauge := metrics.NewGauge()
	histogram := metrics.NewHistogram()

	tags := map[string]string{
		"service":     "example",
		"environment": "development",
		"version":     "1.0.0",
	}

	// Register metrics with the integrated system
	integrator.RegisterCounter("requests_total", counter, tags)
	integrator.RegisterGauge("cpu_usage", gauge, tags)
	integrator.RegisterHistogram("response_time", histogram, tags)

	fmt.Println("📊 Registered metrics: requests_total, cpu_usage, response_time")

	// Simulate some metric data
	fmt.Println("📈 Generating sample data...")

	for i := 0; i < 50; i++ {
		// Simulate request counter
		counter.Inc()

		// Simulate CPU usage gauge (0-100%)
		cpuUsage := 20 + rand.Float64()*60 // 20-80% CPU usage
		gauge.Set(cpuUsage)

		// Simulate response times (histogram)
		responseTime := 50 + rand.Float64()*200 // 50-250ms response times
		histogram.Observe(responseTime)

		// Add direct metric values to enhanced aggregator
		integrator.AddMetricValue("direct_latency", responseTime, tags)
		integrator.AddMetricValue("direct_throughput", float64(i+1), tags)

		// Small delay to simulate real-time data
		time.Sleep(100 * time.Millisecond)

		// Print progress every 10 iterations
		if (i+1)%10 == 0 {
			fmt.Printf("  Generated %d data points...\n", i+1)
		}
	}

	// Give some time for processing
	time.Sleep(1 * time.Second)

	fmt.Println("\n📊 Retrieving Aggregated Statistics")
	fmt.Println("===================================")

	// Get sliding window statistics
	if stats, err := integrator.GetSlidingWindowStats("direct_latency"); err == nil {
		fmt.Printf("🔄 Sliding Window Stats (direct_latency):\n")
		fmt.Printf("   Count: %d\n", stats.Count)
		fmt.Printf("   Mean: %.2f ms\n", stats.Mean)
		fmt.Printf("   Min: %.2f ms\n", stats.Min)
		fmt.Printf("   Max: %.2f ms\n", stats.Max)
		fmt.Printf("   P95: %.2f ms\n", stats.P95)
		fmt.Printf("   P99: %.2f ms\n", stats.P99)
		fmt.Printf("   Window Type: %s\n", stats.WindowType)
		fmt.Printf("   Window Duration: %s\n", stats.WindowDuration)
	} else {
		fmt.Printf("❌ Failed to get sliding window stats: %v\n", err)
	}

	// Get statistical summary
	if summary, err := integrator.GetStatisticalSummary("direct_throughput"); err == nil {
		fmt.Printf("\n📈 Statistical Summary (direct_throughput):\n")
		fmt.Printf("   Count: %d\n", summary.Count)
		fmt.Printf("   Mean: %.2f\n", summary.Mean)
		fmt.Printf("   Min: %.2f\n", summary.Min)
		fmt.Printf("   Max: %.2f\n", summary.Max)
		fmt.Printf("   Variance: %.2f\n", summary.Variance)
		fmt.Printf("   Standard Deviation: %.2f\n", summary.StandardDev)
	} else {
		fmt.Printf("❌ Failed to get statistical summary: %v\n", err)
	}

	// Get time bucket statistics
	startTime := time.Now().Add(-1 * time.Minute)
	endTime := time.Now()
	if bucketStats, err := integrator.GetTimeBucketStats(startTime, endTime); err == nil {
		fmt.Printf("\n🗂️  Time Bucket Stats:\n")
		fmt.Printf("   Total Buckets: %d\n", bucketStats.TotalBuckets)
		fmt.Printf("   Total Entries: %d\n", bucketStats.TotalEntries)
		fmt.Printf("   Memory Usage: %d bytes\n", bucketStats.MemoryUsage)
		fmt.Printf("   Late Data Dropped: %d\n", bucketStats.LateDataDropped)
		fmt.Printf("   Cleanups Run: %d\n", bucketStats.CleanupsRun)
	} else {
		fmt.Printf("❌ Failed to get time bucket stats: %v\n", err)
	}

	// Get basic aggregated values
	if basicValues := integrator.GetBasicAggregatedValues("requests_total"); len(basicValues) > 0 {
		fmt.Printf("\n📊 Basic Aggregated Values (requests_total):\n")
		for i, value := range basicValues {
			if i >= 3 { // Show only first 3 for brevity
				fmt.Printf("   ... and %d more values\n", len(basicValues)-3)
				break
			}
			fmt.Printf("   [%d] Value: %.2f, Count: %d, Type: %v\n",
				i+1, value.Value, value.Count, value.Type)
		}
	}

	// Get collection statistics
	if collectionStats := integrator.GetCollectionStats(); collectionStats != nil {
		fmt.Printf("\n🔄 Collection Statistics:\n")
		fmt.Printf("   Total Events: %d\n", collectionStats.TotalEvents)
		fmt.Printf("   Processed Events: %d\n", collectionStats.ProcessedEvents)
		fmt.Printf("   Failed Events: %d\n", collectionStats.FailedEvents)
		fmt.Printf("   Active Workers: %d\n", collectionStats.ActiveWorkers)
		fmt.Printf("   Average Latency: %d ns\n", collectionStats.AverageLatency)
		fmt.Printf("   Max Latency: %d ns\n", collectionStats.MaxLatency)
	}

	// Show all tracked metrics
	allMetrics := integrator.GetAllMetrics()
	fmt.Printf("\n📋 All Tracked Metrics (%d total):\n", len(allMetrics))
	for i, metric := range allMetrics {
		fmt.Printf("   %d. %s\n", i+1, metric)
	}

	fmt.Println("\n✅ Metrics Integration Example Completed Successfully!")
	fmt.Println("=====================================================")
	fmt.Println("This example demonstrated:")
	fmt.Println("• Creating an integrated metrics system")
	fmt.Println("• Registering different metric types (counter, gauge, histogram)")
	fmt.Println("• Adding direct metric values to enhanced aggregation")
	fmt.Println("• Retrieving sliding window statistics")
	fmt.Println("• Getting statistical summaries")
	fmt.Println("• Accessing time bucket statistics")
	fmt.Println("• Viewing basic aggregated values")
	fmt.Println("• Monitoring collection statistics")
	fmt.Println("")
	fmt.Println("The system successfully integrated:")
	fmt.Println("✓ Basic statistical functions")
	fmt.Println("✓ Percentile calculations")
	fmt.Println("✓ Sliding window aggregation")
	fmt.Println("✓ Time-based bucketing")
	fmt.Println("✓ Metrics collection system")
	fmt.Println("✓ Unified integration interface")
}
