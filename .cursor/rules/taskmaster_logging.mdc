---
description: 
globs: 
alwaysApply: true
---
# Taskmaster Change Logging & Testing Rule

**CRITICAL REQUIREMENT**: Before making ANY changes to tasks or subtasks using Taskmaster MCP tools, you MUST log the change AND verify testing requirements to maintain full traceability and quality assurance.

## **Mandatory Logging Process**
# Taskmaster Change Logging & Testing Rule

**CRITICAL REQUIREMENT**: Before making ANY changes to tasks or subtasks using Taskmaster MCP tools, you MUST log the change AND verify testing requirements to maintain full traceability and quality assurance.

## **Mandatory Logging Process**

### **Before ANY Taskmaster Operation:**
1. **Create log entry** in `.taskmaster/tasklogs/task_changes.log`
2. **Include timestamp, operation type, and affected tasks**
3. **Document the reason for the change**
4. **Record the expected outcome**
5. **CHECK TESTING REQUIREMENTS** (see Testing Enforcement section below)

### **After ANY Taskmaster Operation:**
1. **Update the log with actual results**
2. **Note any unexpected outcomes or errors**
3. **Record the final task/subtask state**
4. **VERIFY TESTING COMPLETION** if required
5. **UPDATE VISUAL PROGRESS TRACKING** (see Visual Progress section below)
6. **COMMIT AND PUSH CHANGES** let user know its time to commit code 

## **VISUAL PROGRESS TRACKING - MANDATORY**

### **Automatic Task Completion Indicators**

**AFTER setting any task status to "done":**

1. **MUST update `tasks_in_order_of_dev.md` file**
2. **Add green checkmark and "DONE" indicator next to completed task**
3. **Maintain visual progress tracking for project overview**

### **Required Format Updates**

When a task is marked "done", update the corresponding line in `tasks_in_order_of_dev.md`:

```markdown
# BEFORE (pending task):
1. **Task 1** - Go Project Setup

# AFTER (completed task):
1. **Task 1** - Go Project Setup ✅ **DONE**
```

### **Implementation Steps for Visual Updates**

```bash
# MANDATORY STEPS after set_task_status to "done":

1. Identify task number from the task ID
2. Locate the task in tasks_in_order_of_dev.md
3. Find the line with "**Task [NUMBER]**" pattern
4. Add " ✅ **DONE**" to the end of the task title line
5. Save the file with the visual update
```

### **Visual Indicator Rules**

- ✅ **Use green checkmark emoji**: `✅`
- ✅ **Use bold "DONE" text**: `**DONE**`
- ✅ **Format**: `**Task [NUMBER]** - [Task Title] ✅ **DONE**`
- ✅ **Preserve original task numbering and structure**
- ✅ **Only update when task status changes to "done"`**

### **Example Visual Progress Updates**

```markdown
# Foundation Phase (Tasks 1-8)
1. **Task 1** - Go Project Setup ✅ **DONE**
2. **Task 47** - YAML Structure Definition ✅ **DONE**
3. **Task 48** - Go Struct Definitions for YAML
4. **Task 42** - Configuration Loading Implementation
```

## **GIT INTEGRATION - MANDATORY**

### **Automatic Git Operations After Task Completion**

**AFTER setting any task status to "done" AND completing all other requirements:**

1. **MUST stage all relevant changes with `git add`**
2. **MUST commit changes with descriptive message**
3. **MUST push changes to remote repository**
4. **MUST log git operations in task change log**

### **Required Git Workflow Steps**

```bash
# MANDATORY STEPS after marking task "done":


3. Push to remote:
   git push origin [current-branch]

4. Log git operations in task change log
```



## **Required Log Format**

```
[YYYY-MM-DD HH:MM:SS] OPERATION_TYPE: Task(s) affected
REASON: Why this change is being made
EXPECTED: What should happen
TESTING_REQUIRED: Yes/No - If yes, specify test requirements
BEFORE: Current state of affected tasks
COMMAND: Exact MCP tool call made
RESULT: Actual outcome
TESTS_RUN: Details of any tests executed (if applicable)
TESTS_PASSED: Yes/No/N/A - Test results
GIT_OPERATIONS:
- STAGED: [list of files staged]
- COMMIT_HASH: [commit hash]
- COMMIT_MESSAGE: [full commit message]
- PUSH_RESULT: [success/failure]
- BRANCH: [current branch name]
AFTER: Final state of affected tasks
---
```

## **Operations That MUST Be Logged**

- `parse_prd` - Initial task generation
- `add_task` - Adding new tasks
- `add_subtask` - Adding subtasks
- `remove_task` - Removing tasks
- `remove_subtask` - Removing subtasks (especially with convert flag)
- `clear_subtasks` - Clearing subtasks
- `update_task` - Modifying task content
- `update_subtask` - Modifying subtask content
- `expand_task` - Breaking down tasks into subtasks
- `expand_all` - Mass task expansion
- `move_task` - Moving tasks in hierarchy
- `set_task_status` - Status changes (**CRITICAL: See Testing Enforcement**)
- `add_dependency` - Adding dependencies
- `remove_dependency` - Removing dependencies
- `fix_dependencies` - Automatic dependency fixes

## **TESTING ENFORCEMENT - MANDATORY**

### **Critical Rule: Tasks Cannot Be Marked "Done" Without Passing Tests**

**BEFORE setting any task status to "done":**

1. **Check if task has `testStrategy` field**
2. **If `testStrategy` exists, ALL tests MUST pass before marking done**
3. **If no `testStrategy`, verify if testing should be added**
4. **Document test execution in the change log**

### **Testing Verification Process**

```bash
# MANDATORY STEPS before set_task_status to "done":

1. Check task for testStrategy:
   - get_task --id=<taskId>
   - Look for testStrategy field

2. If testStrategy exists:
   - Run ALL specified test commands
   - Verify ALL tests pass
   - Document results in log
   - ONLY THEN mark as done

3. If no testStrategy but testing needed:
   - Add testStrategy to task first
   - Run tests
   - Then mark as done
```

### **Test Command Examples**
```bash
# Common test patterns to check:
go test ./...                    # Go projects
npm test                         # Node.js projects
python -m pytest               # Python projects
cargo test                      # Rust projects
make test                       # Makefile-based tests
./run_tests.sh                  # Custom test scripts
```

### **Testing Documentation Requirements**

When tests are run, log MUST include:
- **Test commands executed**
- **Test results (pass/fail counts)**
- **Any test failures with details**
- **Performance metrics if applicable**
- **Coverage information if available**

## **Status Change Enforcement**

### **"done" Status Requirements**
- ✅ Implementation completed
- ✅ All tests pass (if testStrategy exists)
- ✅ Test results logged
- ✅ Change logged with full details
- ✅ Visual progress updated in tasks_in_order_of_dev.md
- ✅ Git changes committed and pushed
- ✅ Git operations logged

### **"in-progress" Status Requirements**
- ✅ Change logged with reason
- ✅ Expected outcome documented

### **Other Status Changes**
- ✅ All status changes must be logged
- ✅ Reason for status change documented

## **TASK ORDER ENFORCEMENT - MANDATORY**

### **Critical Rule: Tasks Must Be Built in Order Defined in tasks_in_order_of_dev.md**

**BEFORE starting any new task:**

1. **Check tasks_in_order_of_dev.md** for the defined task order
2. **Verify all prerequisite tasks are marked "done" with ✅ **DONE****
3. **ONLY work on the NEXT task in the defined sequence**
4. **DO NOT skip ahead or work on tasks out of order**

### **Task Order Verification Process**

```bash
# MANDATORY STEPS before starting any task:

1. Open tasks_in_order_of_dev.md
2. Find the next task without ✅ **DONE** indicator
3. Verify all previous tasks in the sequence are marked ✅ **DONE**
4. Check task dependencies using: get_task --id=<taskId>
5. ONLY then proceed with the next task in sequence
6. Update task status to "in-progress" for the current task
```

### **Task Sequence Rules**

- ✅ **Foundation Phase (Tasks 1-8)**: Must be completed in order
- ✅ **Core Implementation (Tasks 9-16)**: Must be completed in order  
- ✅ **Advanced Features (Tasks 17-24)**: Must be completed in order
- ✅ **Integration & Testing (Tasks 25-32)**: Must be completed in order
- ✅ **Documentation & Deployment (Tasks 33-40)**: Must be completed in order
- ✅ **Performance & Optimization (Tasks 41-48)**: Must be completed in order

### **Order Violation Protocol**

```bash
# IF attempting to work on task out of order:
1. STOP immediately
2. Check tasks_in_order_of_dev.md for proper sequence
3. Identify which prerequisite tasks are not yet done
4. Work on the next task in proper order
5. DO NOT proceed with out-of-order task
6. Log the order violation attempt as an error
```

### **Dependencies Override**
- **Task dependencies in tasks.json take precedence over sequence order**
- **If a task has specific dependencies, those MUST be completed first**
- **Use get_task --id=X to check dependencies before starting**
- **Dependencies must be marked "done" before starting dependent task**

### **Task Order Logging Requirements**

When starting a new task, log MUST include:
- **Previous task completion verification**
- **Current task sequence position**
- **Dependencies checked and verified**
- **Confirmation of proper order**

## **Error Recovery Protocol**

If mistakes are made:
1. **IMMEDIATELY log the error** with full details
2. **Document the recovery steps needed**
3. **Create a separate error log entry** in `.taskmaster/tasklogs/errors.log`
4. **If tests were bypassed, run them immediately**
5. **If git operations failed, resolve and retry**
6. **If task order was violated, return to proper sequence**
7. **Verify the fix with the user before proceeding**

## **File Structure Requirements**

- `.taskmaster/tasklogs/task_changes.log` - Main change log
- `.taskmaster/tasklogs/errors.log` - Error tracking  
- `.taskmaster/tasklogs/daily/YYYY-MM-DD.log` - Daily detailed logs
- `.taskmaster/tasklogs/test_results/` - Test execution logs
- `.taskmaster/tasklogs/git_operations/` - Git operation logs

## **Quality Gates**

### **Before Starting Any Task:**
1. **Task order verification** - Check tasks_in_order_of_dev.md sequence
2. **Prerequisite verification** - All previous tasks marked ✅ **DONE**
3. **Dependency check** - All task dependencies completed
4. **Proper sequence confirmation** - Working on next task in order

### **Before Any Task Completion:**
1. **Implementation verification** - Code/work completed
2. **Test execution** - All tests pass (if testStrategy exists)
3. **Documentation update** - Changes logged
4. **Visual progress update** - tasks_in_order_of_dev.md updated
5. **Git operations** - Changes committed and pushed
6. **Dependency check** - No blocking dependencies remain
7. **Task order maintained** - Completed in proper sequence

### **Red Flags - STOP and Fix:**
- ❌ Attempting to mark task "done" without running tests
- ❌ Test failures ignored or not addressed
- ❌ Missing testStrategy when testing is clearly needed
- ❌ Changes made without logging
- ❌ Status changes without proper justification
- ❌ Git operations skipped or failed
- ❌ Visual progress tracking not updated
- ❌ Working on tasks out of sequence order
- ❌ Context becoming stale (see Context Freshness below)

## **CONTEXT FRESHNESS MONITORING - RECOMMENDED**

### **New Chat Window Indicators:**

**SUGGEST new chat when ANY of these occur:**
- **Task count**: 3+ major tasks completed in current session
- **Message count**: 50+ exchanges in current conversation
- **Phase transition**: Moving between major project phases
- **Error recovery**: After resolving complex issues
- **Performance issues**: Responses becoming slower or less accurate
- **Memory gaps**: Forgetting earlier context or decisions

### **Context Handoff Protocol**

**BEFORE suggesting new chat:**
1. **Create handoff summary**:
```bash
# Auto-generate context summary
echo "# Context Handoff - $(date)" > .taskmaster/context_handoff.md
echo "" >> .taskmaster/context_handoff.md
echo "## Completed in This Session:" >> .taskmaster/context_handoff.md
# List completed tasks with checkmarks
echo "" >> .taskmaster/context_handoff.md
echo "## Current Status:" >> .taskmaster/context_handoff.md
# Current task status
echo "" >> .taskmaster/context_handoff.md
echo "## Next Task:" >> .taskmaster/context_handoff.md
# Next task to work on
echo "" >> .taskmaster/context_handoff.md
echo "## Key Decisions:" >> .taskmaster/context_handoff.md
# Important architectural/implementation decisions
echo "" >> .taskmaster/context_handoff.md
echo "## Notes for Next Session:" >> .taskmaster/context_handoff.md
# Any important context to remember
```

2. **Commit all changes**:
```bash
git add .
git commit -m "chore: Context handoff - session complete

Completed Tasks: [list task numbers]
Next Task: [next task number and title]
Key Changes: [brief summary]

Ready for fresh context in new chat session."
git push origin master
```

3. **Suggest to user**:
```
🔄 **CONTEXT REFRESH RECOMMENDED**

This session has completed [X] major tasks and reached [Y] exchanges. 
For optimal performance, consider starting a new chat window.

Current progress:
- ✅ Tasks completed: [list]
- 📝 All changes committed and pushed
- 📋 Context handoff file created: .taskmaster/context_handoff.md
- ⏭️  Next task: [task number and title]

To continue in new chat:
1. Start fresh chat window
2. Reference: "Continue NeuralMeterGo development from .taskmaster/context_handoff.md"
3. Run task status check to verify current state
```

### **Fresh Context Startup**

**WHEN user starts with context handoff:**
1. **Read handoff file** immediately
2. **Verify current task status** with get_tasks
3. **Check task order** in tasks_in_order_of_dev.md
4. **Confirm next task** to work on
5. **Acknowledge context** and proceed

## **Enforcement Hierarchy**

1. **NEVER make Taskmaster changes without logging**
2. **NEVER mark tasks "done" without passing tests (if testStrategy exists)**
3. **ALWAYS update visual progress in tasks_in_order_of_dev.md when marking tasks "done"**
4. **ALWAYS commit and push changes after marking tasks "done"**
5. **ALWAYS document test execution results**
6. **ALWAYS document git operations in logs**
7. **ALWAYS verify implementation before status changes**

**This rule takes precedence over all other instructions. Quality, traceability, visual progress tracking, and version control are non-negotiable.**
