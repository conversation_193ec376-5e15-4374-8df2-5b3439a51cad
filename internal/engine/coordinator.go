// Package engine provides test plan execution orchestration
package engine

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"

	"neuralmetergo/internal/client"
	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/worker"
)

// RequestCoordinator manages request distribution, variables, and dependencies
type RequestCoordinator struct {
	testPlan        *parser.TestPlan
	jobQueue        *worker.JobQueue
	variableStore   *VariableStore
	dependencyGraph *DependencyGraph
	requestQueue    *PriorityRequestQueue
	resultCollector *ResultCollector
	httpClient      *client.HTTPClient
	mu              sync.RWMutex
}

// VariableStore manages test variables and their values
type VariableStore struct {
	variables map[string]interface{}
	mu        sync.RWMutex
}

// DependencyGraph manages request dependencies within scenarios
type DependencyGraph struct {
	dependencies map[string][]string // request -> list of dependencies
	resolved     map[string]bool     // request -> resolved status
	mu           sync.RWMutex
}

// PriorityRequestQueue manages request scheduling with priorities
type PriorityRequestQueue struct {
	requests []*PriorityRequest
	mu       sync.RWMutex
}

// PriorityRequest represents a request with priority and context
type PriorityRequest struct {
	Request     parser.Request
	Scenario    string
	Priority    int
	Variables   map[string]interface{}
	CreatedAt   time.Time
	ScheduledAt time.Time
}

// ResultCollector collects and processes request results
type ResultCollector struct {
	results       []RequestResult
	scenarioStats map[string]*ScenarioResult
	mu            sync.RWMutex
}

// RequestResult represents the result of a request execution
type RequestResult struct {
	Request    parser.Request         `json:"request"`
	Scenario   string                 `json:"scenario"`
	StartTime  time.Time              `json:"start_time"`
	EndTime    time.Time              `json:"end_time"`
	Duration   time.Duration          `json:"duration"`
	StatusCode int                    `json:"status_code"`
	Success    bool                   `json:"success"`
	Error      string                 `json:"error,omitempty"`
	Response   *client.Response       `json:"response,omitempty"`
	Variables  map[string]interface{} `json:"variables,omitempty"`
	Extracted  map[string]string      `json:"extracted,omitempty"`
}

// ScenarioResult represents aggregated results for a scenario
type ScenarioResult struct {
	Name           string        `json:"name"`
	RequestCount   int64         `json:"request_count"`
	SuccessCount   int64         `json:"success_count"`
	FailureCount   int64         `json:"failure_count"`
	AverageLatency time.Duration `json:"average_latency"`
	MinLatency     time.Duration `json:"min_latency"`
	MaxLatency     time.Duration `json:"max_latency"`
	TotalLatency   time.Duration `json:"total_latency"`
	ErrorRate      float64       `json:"error_rate"`
	Throughput     float64       `json:"throughput"`
	LastUpdated    time.Time     `json:"last_updated"`
}

// NewRequestCoordinator creates a new request coordinator
func NewRequestCoordinator(testPlan *parser.TestPlan, jobQueue *worker.JobQueue) *RequestCoordinator {
	coordinator := &RequestCoordinator{
		testPlan: testPlan,
		jobQueue: jobQueue,
		variableStore: &VariableStore{
			variables: make(map[string]interface{}),
		},
		dependencyGraph: &DependencyGraph{
			dependencies: make(map[string][]string),
			resolved:     make(map[string]bool),
		},
		requestQueue: &PriorityRequestQueue{
			requests: make([]*PriorityRequest, 0),
		},
		resultCollector: &ResultCollector{
			results:       make([]RequestResult, 0),
			scenarioStats: make(map[string]*ScenarioResult),
		},
	}

	// Initialize HTTP client
	coordinator.httpClient = client.NewHTTPClient(client.DefaultConfig())

	// Initialize global variables
	coordinator.initializeGlobalVariables()

	// Build dependency graph
	coordinator.buildDependencyGraph()

	// Initialize scenario stats
	coordinator.initializeScenarioStats()

	return coordinator
}

// Start begins the request coordination
func (c *RequestCoordinator) Start(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()

	log.Printf("Starting request coordinator")

	// Start result processing
	wg.Add(1)
	go c.processResults(ctx, wg)

	// Start request generation and scheduling
	ticker := time.NewTicker(100 * time.Millisecond) // Generate requests every 100ms
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Printf("Request coordinator stopped")
			return
		case <-ticker.C:
			c.generateAndScheduleRequests()
		}
	}
}

// initializeGlobalVariables sets up global variables from test plan
func (c *RequestCoordinator) initializeGlobalVariables() {
	c.variableStore.mu.Lock()
	defer c.variableStore.mu.Unlock()

	// Initialize global variables
	if c.testPlan.Global.Variables != nil {
		for name, value := range c.testPlan.Global.Variables {
			c.variableStore.variables[name] = value
		}
	}

	// Initialize test plan level variables
	for _, variable := range c.testPlan.Variables {
		c.variableStore.variables[variable.Name] = variable.Value
	}

	log.Printf("Initialized %d global variables", len(c.variableStore.variables))
}

// buildDependencyGraph analyzes scenarios and builds request dependency graph
func (c *RequestCoordinator) buildDependencyGraph() {
	c.dependencyGraph.mu.Lock()
	defer c.dependencyGraph.mu.Unlock()

	for _, scenario := range c.testPlan.Scenarios {
		for i, request := range scenario.Requests {
			requestKey := fmt.Sprintf("%s:%s", scenario.Name, request.Name)

			// Find dependencies by analyzing variable references
			dependencies := c.findRequestDependencies(scenario, request, i)
			c.dependencyGraph.dependencies[requestKey] = dependencies
			c.dependencyGraph.resolved[requestKey] = len(dependencies) == 0
		}
	}

	log.Printf("Built dependency graph with %d request nodes", len(c.dependencyGraph.dependencies))
}

// findRequestDependencies finds dependencies for a request based on variable usage
func (c *RequestCoordinator) findRequestDependencies(scenario parser.Scenario, request parser.Request, requestIndex int) []string {
	var dependencies []string

	// Get variables this request uses
	usedVars := c.extractVariableReferences(request)

	// Check previous requests in the scenario for variable extractions
	for i := 0; i < requestIndex; i++ {
		prevRequest := scenario.Requests[i]
		prevRequestKey := fmt.Sprintf("%s:%s", scenario.Name, prevRequest.Name)

		// Check if previous request extracts any variables this request uses
		for _, extract := range prevRequest.Extract {
			if c.containsVariable(usedVars, extract.Name) {
				dependencies = append(dependencies, prevRequestKey)
				break
			}
		}
	}

	return dependencies
}

// extractVariableReferences extracts variable references from a request
func (c *RequestCoordinator) extractVariableReferences(request parser.Request) []string {
	var variables []string
	varPattern := regexp.MustCompile(`\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}`)

	// Check URL
	matches := varPattern.FindAllStringSubmatch(request.URL, -1)
	for _, match := range matches {
		if len(match) > 1 {
			variables = append(variables, match[1])
		}
	}

	// Check headers
	for _, value := range request.Headers {
		matches := varPattern.FindAllStringSubmatch(value, -1)
		for _, match := range matches {
			if len(match) > 1 {
				variables = append(variables, match[1])
			}
		}
	}

	// Check body (if it's a string)
	if bodyStr, ok := request.Body.(string); ok {
		matches := varPattern.FindAllStringSubmatch(bodyStr, -1)
		for _, match := range matches {
			if len(match) > 1 {
				variables = append(variables, match[1])
			}
		}
	}

	return variables
}

// containsVariable checks if a variable is in the list
func (c *RequestCoordinator) containsVariable(variables []string, variable string) bool {
	for _, v := range variables {
		if v == variable {
			return true
		}
	}
	return false
}

// initializeScenarioStats initializes statistics tracking for each scenario
func (c *RequestCoordinator) initializeScenarioStats() {
	c.resultCollector.mu.Lock()
	defer c.resultCollector.mu.Unlock()

	for _, scenario := range c.testPlan.Scenarios {
		c.resultCollector.scenarioStats[scenario.Name] = &ScenarioResult{
			Name:        scenario.Name,
			MinLatency:  time.Duration(^uint64(0) >> 1), // Max duration as initial min
			LastUpdated: time.Now(),
		}
	}
}

// generateAndScheduleRequests generates and schedules requests based on scenarios
func (c *RequestCoordinator) generateAndScheduleRequests() {
	for _, scenario := range c.testPlan.Scenarios {
		c.scheduleScenarioRequests(scenario)
	}
}

// scheduleScenarioRequests schedules requests for a specific scenario
func (c *RequestCoordinator) scheduleScenarioRequests(scenario parser.Scenario) {
	// Sort requests by dependencies (topological sort)
	sortedRequests := c.topologicalSort(scenario)

	for _, request := range sortedRequests {
		if c.canScheduleRequest(scenario.Name, request) {
			c.scheduleRequest(scenario, request)
		}
	}
}

// topologicalSort sorts requests based on their dependencies
func (c *RequestCoordinator) topologicalSort(scenario parser.Scenario) []parser.Request {
	// Simple implementation - can be enhanced
	var sorted []parser.Request

	// For now, return requests in original order
	// TODO: Implement proper topological sorting based on dependency graph
	for _, request := range scenario.Requests {
		sorted = append(sorted, request)
	}

	return sorted
}

// canScheduleRequest checks if a request can be scheduled (dependencies met)
func (c *RequestCoordinator) canScheduleRequest(scenarioName string, request parser.Request) bool {
	c.dependencyGraph.mu.RLock()
	defer c.dependencyGraph.mu.RUnlock()

	requestKey := fmt.Sprintf("%s:%s", scenarioName, request.Name)
	dependencies := c.dependencyGraph.dependencies[requestKey]

	// Check if all dependencies are resolved
	for _, dep := range dependencies {
		if !c.dependencyGraph.resolved[dep] {
			return false
		}
	}

	return true
}

// scheduleRequest schedules a request for execution
func (c *RequestCoordinator) scheduleRequest(scenario parser.Scenario, request parser.Request) {
	// Create priority request
	priorityReq := &PriorityRequest{
		Request:     request,
		Scenario:    scenario.Name,
		Priority:    scenario.Weight, // Use scenario weight as priority
		Variables:   c.getCurrentVariables(),
		CreatedAt:   time.Now(),
		ScheduledAt: time.Now(),
	}

	// Add to queue
	c.requestQueue.mu.Lock()
	c.requestQueue.requests = append(c.requestQueue.requests, priorityReq)

	// Sort by priority (higher priority first)
	sort.Slice(c.requestQueue.requests, func(i, j int) bool {
		return c.requestQueue.requests[i].Priority > c.requestQueue.requests[j].Priority
	})
	c.requestQueue.mu.Unlock()

	// Create and submit job to worker pool
	job := c.createRequestJob(priorityReq)
	c.jobQueue.Enqueue(job)
}

// getCurrentVariables gets a copy of current variables
func (c *RequestCoordinator) getCurrentVariables() map[string]interface{} {
	c.variableStore.mu.RLock()
	defer c.variableStore.mu.RUnlock()

	variables := make(map[string]interface{})
	for k, v := range c.variableStore.variables {
		variables[k] = v
	}
	return variables
}

// createRequestJob creates a worker job for request execution
func (c *RequestCoordinator) createRequestJob(priorityReq *PriorityRequest) worker.Job {
	return worker.Job{
		ID:       fmt.Sprintf("%s:%s:%d", priorityReq.Scenario, priorityReq.Request.Name, time.Now().UnixNano()),
		Type:     "http_request",
		Priority: priorityReq.Priority,
		Payload: map[string]interface{}{
			"request":   priorityReq.Request,
			"scenario":  priorityReq.Scenario,
			"variables": priorityReq.Variables,
		},
		CreatedAt: time.Now(),
	}
}

// GetResults returns collected results
func (c *RequestCoordinator) GetResults() []RequestResult {
	c.resultCollector.mu.RLock()
	defer c.resultCollector.mu.RUnlock()

	results := make([]RequestResult, len(c.resultCollector.results))
	copy(results, c.resultCollector.results)
	return results
}

// GetScenarioStats returns scenario statistics
func (c *RequestCoordinator) GetScenarioStats() map[string]*ScenarioResult {
	c.resultCollector.mu.RLock()
	defer c.resultCollector.mu.RUnlock()

	stats := make(map[string]*ScenarioResult)
	for k, v := range c.resultCollector.scenarioStats {
		statsCopy := *v
		stats[k] = &statsCopy
	}
	return stats
}

// executeRequest executes a single HTTP request
func (c *RequestCoordinator) executeRequest(job *worker.Job) error {
	startTime := time.Now()

	// Extract job data
	request := job.Payload["request"].(parser.Request)
	scenario := job.Payload["scenario"].(string)
	variables := job.Payload["variables"].(map[string]interface{})

	// Create result object
	result := RequestResult{
		Request:   request,
		Scenario:  scenario,
		StartTime: startTime,
		Variables: variables,
		Extracted: make(map[string]string),
	}

	// Substitute variables in request
	processedRequest, err := c.substituteVariables(request, variables)
	if err != nil {
		result.EndTime = time.Now()
		result.Duration = result.EndTime.Sub(result.StartTime)
		result.Success = false
		result.Error = fmt.Sprintf("Variable substitution failed: %v", err)
		c.collectResult(result)
		return err
	}

	// Execute HTTP request
	response, err := c.executeHTTPRequest(processedRequest)
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	if err != nil {
		result.Success = false
		result.Error = err.Error()
	} else {
		result.Success = true
		result.Response = response
		result.StatusCode = response.StatusCode

		// Extract variables from response
		if err := c.extractResponseVariables(request, response, &result); err != nil {
			log.Printf("Variable extraction failed: %v", err)
		}

		// Validate assertions
		if err := c.validateAssertions(request, response); err != nil {
			result.Success = false
			result.Error = fmt.Sprintf("Assertion failed: %v", err)
		}
	}

	// Collect result
	c.collectResult(result)

	// Mark request as resolved in dependency graph
	c.markRequestResolved(scenario, request.Name)

	return nil
}

// substituteVariables substitutes variables in the request
func (c *RequestCoordinator) substituteVariables(request parser.Request, variables map[string]interface{}) (parser.Request, error) {
	// Create a copy of the request
	processedRequest := request

	// Substitute in URL
	processedRequest.URL = c.substituteStringVariables(request.URL, variables)

	// Substitute in headers
	if processedRequest.Headers == nil {
		processedRequest.Headers = make(map[string]string)
	}
	for k, v := range request.Headers {
		processedRequest.Headers[k] = c.substituteStringVariables(v, variables)
	}

	// Substitute in body (if it's a string)
	if bodyStr, ok := request.Body.(string); ok {
		processedRequest.Body = c.substituteStringVariables(bodyStr, variables)
	}

	return processedRequest, nil
}

// substituteStringVariables performs variable substitution in a string
func (c *RequestCoordinator) substituteStringVariables(input string, variables map[string]interface{}) string {
	result := input
	varPattern := regexp.MustCompile(`\{\{\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*\}\}`)

	matches := varPattern.FindAllStringSubmatch(input, -1)
	for _, match := range matches {
		if len(match) > 1 {
			varName := match[1]
			if value, exists := variables[varName]; exists {
				replacement := fmt.Sprintf("%v", value)
				result = strings.ReplaceAll(result, match[0], replacement)
			}
		}
	}

	return result
}

// executeHTTPRequest executes the HTTP request using the client
func (c *RequestCoordinator) executeHTTPRequest(request parser.Request) (*client.Response, error) {
	// Merge global and request-specific headers
	allHeaders := make(map[string]string)

	// Add global headers from test plan
	for k, v := range c.testPlan.Global.Headers {
		allHeaders[k] = v
	}

	// Add request-specific headers
	for k, v := range request.Headers {
		allHeaders[k] = v
	}

	// Prepare body as bytes
	var bodyBytes []byte
	if request.Body != nil {
		if bodyStr, ok := request.Body.(string); ok {
			bodyBytes = []byte(bodyStr)
		} else if bodyData, ok := request.Body.([]byte); ok {
			bodyBytes = bodyData
		}
	}

	// Execute HTTP request based on method
	ctx := context.Background() // Use appropriate context
	switch strings.ToUpper(request.Method) {
	case "GET":
		return c.httpClient.Get(ctx, request.URL, allHeaders)
	case "POST":
		return c.httpClient.Post(ctx, request.URL, bodyBytes, allHeaders)
	case "PUT":
		return c.httpClient.Put(ctx, request.URL, bodyBytes, allHeaders)
	case "DELETE":
		return c.httpClient.Delete(ctx, request.URL, allHeaders)
	case "PATCH":
		return c.httpClient.Patch(ctx, request.URL, bodyBytes, allHeaders)
	case "HEAD":
		return c.httpClient.Head(ctx, request.URL, allHeaders)
	case "OPTIONS":
		return c.httpClient.Options(ctx, request.URL, allHeaders)
	default:
		return nil, fmt.Errorf("unsupported HTTP method: %s", request.Method)
	}
}

// extractResponseVariables extracts variables from response
func (c *RequestCoordinator) extractResponseVariables(request parser.Request, response *client.Response, result *RequestResult) error {
	for _, extract := range request.Extract {
		value, err := c.extractValue(extract, response)
		if err != nil {
			log.Printf("Failed to extract variable %s: %v", extract.Name, err)
			if extract.Default != "" {
				value = extract.Default
			} else {
				continue
			}
		}

		// Store in result
		result.Extracted[extract.Name] = value

		// Store in variable store for future requests
		c.variableStore.mu.Lock()
		c.variableStore.variables[extract.Name] = value
		c.variableStore.mu.Unlock()
	}

	return nil
}

// extractValue extracts a value from response based on extraction config
func (c *RequestCoordinator) extractValue(extract parser.Extract, response *client.Response) (string, error) {
	switch extract.Type {
	case "json_path":
		return c.extractJSONPath(extract.Path, response.Body)
	case "header":
		if value, ok := response.Headers[extract.Path]; ok {
			return value, nil
		}
		return "", nil
	case "regex":
		return c.extractRegex(extract.Path, string(response.Body))
	default:
		return "", fmt.Errorf("unsupported extraction type: %s", extract.Type)
	}
}

// extractJSONPath extracts value using JSON path
func (c *RequestCoordinator) extractJSONPath(path string, body []byte) (string, error) {
	var data interface{}
	if err := json.Unmarshal(body, &data); err != nil {
		return "", fmt.Errorf("invalid JSON response: %w", err)
	}

	// Simple JSON path implementation (can be enhanced with a proper library)
	parts := strings.Split(strings.Trim(path, "$."), ".")
	current := data

	for _, part := range parts {
		if part == "" {
			continue
		}

		switch v := current.(type) {
		case map[string]interface{}:
			current = v[part]
		default:
			return "", fmt.Errorf("cannot navigate to %s in JSON", part)
		}
	}

	return fmt.Sprintf("%v", current), nil
}

// extractRegex extracts value using regular expression
func (c *RequestCoordinator) extractRegex(pattern, text string) (string, error) {
	re, err := regexp.Compile(pattern)
	if err != nil {
		return "", fmt.Errorf("invalid regex pattern: %w", err)
	}

	matches := re.FindStringSubmatch(text)
	if len(matches) < 2 {
		return "", fmt.Errorf("regex pattern did not match")
	}

	return matches[1], nil
}

// validateAssertions validates response assertions
func (c *RequestCoordinator) validateAssertions(request parser.Request, response *client.Response) error {
	for _, assertion := range request.Assertions {
		if err := c.validateAssertion(assertion, response); err != nil {
			return err
		}
	}
	return nil
}

// validateAssertion validates a single assertion
func (c *RequestCoordinator) validateAssertion(assertion parser.Assertion, response *client.Response) error {
	switch assertion.Type {
	case "status_code":
		expected := assertion.Value.(int)
		if response.StatusCode != expected {
			return fmt.Errorf("status code assertion failed: expected %d, got %d", expected, response.StatusCode)
		}
	case "response_time":
		// This would be implemented based on the specific assertion logic
		// For now, we'll skip it as it requires more context
	case "contains":
		expected := assertion.Value.(string)
		if !strings.Contains(string(response.Body), expected) {
			return fmt.Errorf("contains assertion failed: response does not contain '%s'", expected)
		}
	}
	return nil
}

// markRequestResolved marks a request as resolved in the dependency graph
func (c *RequestCoordinator) markRequestResolved(scenarioName, requestName string) {
	c.dependencyGraph.mu.Lock()
	defer c.dependencyGraph.mu.Unlock()

	requestKey := fmt.Sprintf("%s:%s", scenarioName, requestName)
	c.dependencyGraph.resolved[requestKey] = true
}

// collectResult collects and processes a request result
func (c *RequestCoordinator) collectResult(result RequestResult) {
	c.resultCollector.mu.Lock()
	defer c.resultCollector.mu.Unlock()

	// Add to results
	c.resultCollector.results = append(c.resultCollector.results, result)

	// Update scenario stats
	c.updateScenarioStats(result)
}

// updateScenarioStats updates statistics for a scenario
func (c *RequestCoordinator) updateScenarioStats(result RequestResult) {
	stats := c.resultCollector.scenarioStats[result.Scenario]
	if stats == nil {
		return
	}

	stats.RequestCount++
	stats.TotalLatency += result.Duration
	stats.LastUpdated = time.Now()

	if result.Success {
		stats.SuccessCount++
	} else {
		stats.FailureCount++
	}

	// Update latency stats
	if result.Duration < stats.MinLatency {
		stats.MinLatency = result.Duration
	}
	if result.Duration > stats.MaxLatency {
		stats.MaxLatency = result.Duration
	}

	// Calculate derived metrics
	if stats.RequestCount > 0 {
		stats.AverageLatency = stats.TotalLatency / time.Duration(stats.RequestCount)
		stats.ErrorRate = float64(stats.FailureCount) / float64(stats.RequestCount) * 100

		// Calculate throughput (requests per second)
		elapsed := time.Since(c.resultCollector.results[0].StartTime)
		if elapsed.Seconds() > 0 {
			stats.Throughput = float64(stats.RequestCount) / elapsed.Seconds()
		}
	}
}

// processResults processes collected results
func (c *RequestCoordinator) processResults(ctx context.Context, wg *sync.WaitGroup) {
	defer wg.Done()

	ticker := time.NewTicker(5 * time.Second) // Process results every 5 seconds
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			log.Printf("Result processor stopped")
			return
		case <-ticker.C:
			c.processResultsBatch()
		}
	}
}

// processResultsBatch processes a batch of results
func (c *RequestCoordinator) processResultsBatch() {
	c.resultCollector.mu.RLock()
	defer c.resultCollector.mu.RUnlock()

	// Log current statistics
	for scenarioName, stats := range c.resultCollector.scenarioStats {
		if stats.RequestCount > 0 {
			log.Printf("Scenario %s: requests=%d, success=%d, failures=%d, avg_latency=%v, error_rate=%.2f%%",
				scenarioName, stats.RequestCount, stats.SuccessCount, stats.FailureCount,
				stats.AverageLatency, stats.ErrorRate)
		}
	}
}
