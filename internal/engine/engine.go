// Package engine provides test plan execution orchestration
package engine

import (
	"context"
	"fmt"
	"log"
	"sync"
	"sync/atomic"
	"time"

	"neuralmetergo/internal/parser"
	"neuralmetergo/internal/validation"
	"neuralmetergo/internal/worker"
)

// ExecutionStatus represents the current state of execution
type ExecutionStatus int

const (
	StatusPending ExecutionStatus = iota
	StatusInitializing
	StatusRampingUp
	StatusRunning
	StatusStopping
	StatusCompleted
	StatusFailed
)

func (s ExecutionStatus) String() string {
	switch s {
	case StatusPending:
		return "pending"
	case StatusInitializing:
		return "initializing"
	case StatusRampingUp:
		return "ramping_up"
	case StatusRunning:
		return "running"
	case StatusStopping:
		return "stopping"
	case StatusCompleted:
		return "completed"
	case StatusFailed:
		return "failed"
	default:
		return "unknown"
	}
}

// EngineConfig configures the execution engine behavior
type EngineConfig struct {
	MaxWorkers          int           `json:"max_workers" yaml:"max_workers"`
	WorkerQueueCapacity int           `json:"worker_queue_capacity" yaml:"worker_queue_capacity"`
	MetricsInterval     time.Duration `json:"metrics_interval" yaml:"metrics_interval"`
	StopTimeout         time.Duration `json:"stop_timeout" yaml:"stop_timeout"`
	EnableDebugLogs     bool          `json:"enable_debug_logs" yaml:"enable_debug_logs"`
}

// DefaultEngineConfig returns a default configuration for the execution engine
func DefaultEngineConfig() *EngineConfig {
	return &EngineConfig{
		MaxWorkers:          100,
		WorkerQueueCapacity: 10000,
		MetricsInterval:     1 * time.Second,
		StopTimeout:         30 * time.Second,
		EnableDebugLogs:     false,
	}
}

// ExecutionMetrics tracks execution performance
type ExecutionMetrics struct {
	StartTime         time.Time     `json:"start_time"`
	EndTime           time.Time     `json:"end_time,omitempty"`
	Duration          time.Duration `json:"duration"`
	RequestsExecuted  int64         `json:"requests_executed"`
	RequestsSucceeded int64         `json:"requests_succeeded"`
	RequestsFailed    int64         `json:"requests_failed"`
	AverageLatency    time.Duration `json:"average_latency"`
	MinLatency        time.Duration `json:"min_latency"`
	MaxLatency        time.Duration `json:"max_latency"`
	Throughput        float64       `json:"throughput"` // requests per second
	ErrorRate         float64       `json:"error_rate"` // percentage
	ActiveWorkers     int32         `json:"active_workers"`
	TotalWorkers      int32         `json:"total_workers"`
	LastUpdated       time.Time     `json:"last_updated"`
}

// ExecutionState tracks the current execution state
type ExecutionState struct {
	Status            ExecutionStatus    `json:"status"`
	StartTime         time.Time          `json:"start_time,omitempty"`
	ElapsedTime       time.Duration      `json:"elapsed_time"`
	RemainingTime     time.Duration      `json:"remaining_time"`
	Progress          float64            `json:"progress"` // 0.0 to 1.0
	CurrentWorkers    int32              `json:"current_workers"`
	TargetWorkers     int32              `json:"target_workers"`
	RequestsExecuted  int64              `json:"requests_executed"`
	RequestsRemaining int64              `json:"requests_remaining"`
	LastError         string             `json:"last_error,omitempty"`
	ExecutionErrors   []string           `json:"execution_errors,omitempty"`
	ScenarioProgress  map[string]float64 `json:"scenario_progress,omitempty"`
	mu                sync.RWMutex
}

// ExecutionEngine orchestrates test plan execution
type ExecutionEngine struct {
	testPlan      *parser.TestPlan
	config        *EngineConfig
	workerPool    *worker.WorkerPool
	jobQueue      *worker.JobQueue
	scheduler     *ExecutionScheduler
	coordinator   *RequestCoordinator
	state         *ExecutionState
	metrics       *ExecutionMetrics
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	mu            sync.RWMutex
	stopChan      chan bool
	metricsTicker *time.Ticker
}

// NewExecutionEngine creates a new execution engine with the given test plan and configuration
func NewExecutionEngine(testPlan *parser.TestPlan, config *EngineConfig) *ExecutionEngine {
	if config == nil {
		config = DefaultEngineConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &ExecutionEngine{
		testPlan: testPlan,
		config:   config,
		ctx:      ctx,
		cancel:   cancel,
		state: &ExecutionState{
			Status:           StatusPending,
			ScenarioProgress: make(map[string]float64),
		},
		metrics:  &ExecutionMetrics{},
		stopChan: make(chan bool, 1),
	}
}

// Initialize prepares the execution engine for running
func (e *ExecutionEngine) Initialize() error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.state.Status != StatusPending {
		return fmt.Errorf("execution engine is not in pending state (current: %s)", e.state.Status)
	}

	e.state.Status = StatusInitializing
	log.Printf("Initializing execution engine for test plan: %s", e.testPlan.Name)

	// Validate test plan first
	if err := e.validateTestPlan(); err != nil {
		e.state.Status = StatusFailed
		e.state.LastError = fmt.Sprintf("Test plan validation failed: %v", err)
		return err
	}

	// Calculate target workers based on concurrency
	targetWorkers := e.testPlan.Concurrency
	if targetWorkers > e.config.MaxWorkers {
		targetWorkers = e.config.MaxWorkers
		log.Printf("Limiting workers to max allowed: %d (requested: %d)", targetWorkers, e.testPlan.Concurrency)
	}
	e.state.TargetWorkers = int32(targetWorkers)

	// Create job queue
	e.jobQueue = worker.NewJobQueue(e.config.WorkerQueueCapacity)

	// Create worker pool
	poolConfig := worker.DefaultPoolConfig()
	poolConfig.MaxWorkers = targetWorkers
	poolConfig.MinWorkers = 1
	poolConfig.AutoScale = true
	e.workerPool = worker.NewWorkerPoolWithConfig(poolConfig, e.jobQueue)

	// Initialize scheduler
	e.scheduler = NewExecutionScheduler(e.testPlan, e.config)

	// Initialize request coordinator
	e.coordinator = NewRequestCoordinator(e.testPlan, e.jobQueue)

	// Initialize metrics
	e.metrics.StartTime = time.Now()
	e.metrics.LastUpdated = time.Now()

	// Initialize scenario progress tracking
	for _, scenario := range e.testPlan.Scenarios {
		e.state.ScenarioProgress[scenario.Name] = 0.0
	}

	log.Printf("Execution engine initialized successfully")
	return nil
}

// Start begins the test plan execution
func (e *ExecutionEngine) Start() error {
	e.mu.Lock()
	defer e.mu.Unlock()

	if e.state.Status != StatusInitializing {
		return fmt.Errorf("execution engine must be initialized before starting (current: %s)", e.state.Status)
	}

	log.Printf("Starting execution engine")
	e.state.StartTime = time.Now()
	e.state.Status = StatusRampingUp

	// Start worker pool
	if err := e.workerPool.Start(); err != nil {
		e.state.Status = StatusFailed
		e.state.LastError = fmt.Sprintf("Failed to start worker pool: %v", err)
		return err
	}

	// Start metrics collection
	e.startMetricsCollection()

	// Start execution orchestration
	e.wg.Add(1)
	go e.orchestrateExecution()

	log.Printf("Execution engine started successfully")
	return nil
}

// Stop gracefully stops the execution
func (e *ExecutionEngine) Stop() error {
	e.mu.Lock()
	currentStatus := e.state.Status
	if currentStatus == StatusStopping || currentStatus == StatusCompleted || currentStatus == StatusFailed {
		e.mu.Unlock()
		return nil // Already stopping or stopped
	}

	e.state.Status = StatusStopping
	e.mu.Unlock()

	log.Printf("Stopping execution engine")

	// Signal stop to orchestration
	select {
	case e.stopChan <- true:
	default:
	}

	// Cancel context to signal all goroutines
	e.cancel()

	// Wait for orchestration to complete with timeout
	done := make(chan struct{})
	go func() {
		e.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		log.Printf("Orchestration stopped gracefully")
	case <-time.After(e.config.StopTimeout):
		log.Printf("Stop timeout reached, forcing shutdown")
	}

	// Stop worker pool
	if e.workerPool != nil {
		if err := e.workerPool.Stop(); err != nil {
			log.Printf("Error stopping worker pool: %v", err)
		}
	}

	// Stop metrics collection
	e.stopMetricsCollection()

	// Finalize metrics
	e.mu.Lock()
	e.metrics.EndTime = time.Now()
	e.metrics.Duration = e.metrics.EndTime.Sub(e.metrics.StartTime)
	e.state.Status = StatusCompleted
	e.mu.Unlock()

	log.Printf("Execution engine stopped")
	return nil
}

// GetState returns a copy of the current execution state
func (e *ExecutionEngine) GetState() ExecutionState {
	e.state.mu.RLock()
	defer e.state.mu.RUnlock()

	state := *e.state
	state.ScenarioProgress = make(map[string]float64)
	for k, v := range e.state.ScenarioProgress {
		state.ScenarioProgress[k] = v
	}
	return state
}

// GetMetrics returns a copy of the current execution metrics
func (e *ExecutionEngine) GetMetrics() ExecutionMetrics {
	e.mu.RLock()
	defer e.mu.RUnlock()

	metrics := *e.metrics
	return metrics
}

// validateTestPlan validates the test plan using the validation engine
func (e *ExecutionEngine) validateTestPlan() error {
	validationEngine := validation.NewValidationEngine()
	result := validationEngine.ValidateTestPlan(e.testPlan)

	if !result.IsValid() {
		// Collect error messages
		var errors []string
		for _, issue := range result.Issues {
			if issue.Severity == validation.SeverityError {
				errors = append(errors, issue.Message)
			}
		}
		return fmt.Errorf("test plan validation failed: %v", errors)
	}

	return nil
}

// orchestrateExecution manages the overall execution flow
func (e *ExecutionEngine) orchestrateExecution() {
	defer e.wg.Done()

	log.Printf("Starting execution orchestration")

	// Phase 1: Ramp-up (if specified)
	if e.testPlan.RampUp.Duration > 0 {
		if err := e.executeRampUp(); err != nil {
			e.handleExecutionError(fmt.Errorf("ramp-up failed: %w", err))
			return
		}
	}

	// Phase 2: Main execution
	e.mu.Lock()
	e.state.Status = StatusRunning
	e.mu.Unlock()

	if err := e.executeMainPhase(); err != nil {
		e.handleExecutionError(fmt.Errorf("main execution failed: %w", err))
		return
	}

	log.Printf("Execution orchestration completed successfully")
}

// executeRampUp handles the ramp-up phase
func (e *ExecutionEngine) executeRampUp() error {
	log.Printf("Starting ramp-up phase (duration: %v)", e.testPlan.RampUp.Duration)

	// Calculate ramp-up steps
	steps := 10 // Number of ramp-up steps
	stepDuration := e.testPlan.RampUp.Duration / time.Duration(steps)
	workersPerStep := int(e.state.TargetWorkers) / steps

	for step := 1; step <= steps; step++ {
		select {
		case <-e.ctx.Done():
			return fmt.Errorf("execution cancelled during ramp-up")
		case <-e.stopChan:
			return fmt.Errorf("execution stopped during ramp-up")
		default:
		}

		// Calculate current worker target
		currentTarget := workersPerStep * step
		if step == steps {
			currentTarget = int(e.state.TargetWorkers) // Ensure we reach the exact target
		}

		// Update worker count (worker pool handles auto-scaling)
		atomic.StoreInt32(&e.state.CurrentWorkers, int32(currentTarget))

		log.Printf("Ramp-up step %d/%d: targeting %d workers", step, steps, currentTarget)

		// Wait for step duration
		time.Sleep(stepDuration)
	}

	log.Printf("Ramp-up phase completed")
	return nil
}

// executeMainPhase handles the main execution phase
func (e *ExecutionEngine) executeMainPhase() error {
	log.Printf("Starting main execution phase (duration: %v)", e.testPlan.Duration.Duration)

	// Start request scheduling
	e.wg.Add(1)
	go e.scheduler.Start(e.ctx, &e.wg)

	// Start request coordination
	e.wg.Add(1)
	go e.coordinator.Start(e.ctx, &e.wg)

	// Wait for test duration or stop signal
	timer := time.NewTimer(e.testPlan.Duration.Duration)
	defer timer.Stop()

	select {
	case <-timer.C:
		log.Printf("Test duration completed")
	case <-e.ctx.Done():
		log.Printf("Execution cancelled")
	case <-e.stopChan:
		log.Printf("Execution stopped by user")
	}

	return nil
}

// handleExecutionError handles errors during execution
func (e *ExecutionEngine) handleExecutionError(err error) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.state.Status = StatusFailed
	e.state.LastError = err.Error()
	e.state.ExecutionErrors = append(e.state.ExecutionErrors, err.Error())

	log.Printf("Execution error: %v", err)
}

// startMetricsCollection starts the metrics collection routine
func (e *ExecutionEngine) startMetricsCollection() {
	e.metricsTicker = time.NewTicker(e.config.MetricsInterval)

	e.wg.Add(1)
	go func() {
		defer e.wg.Done()
		defer e.metricsTicker.Stop()

		for {
			select {
			case <-e.metricsTicker.C:
				e.updateMetrics()
			case <-e.ctx.Done():
				return
			}
		}
	}()
}

// stopMetricsCollection stops the metrics collection
func (e *ExecutionEngine) stopMetricsCollection() {
	if e.metricsTicker != nil {
		e.metricsTicker.Stop()
	}
}

// updateMetrics updates the current execution metrics
func (e *ExecutionEngine) updateMetrics() {
	e.mu.Lock()
	defer e.mu.Unlock()

	now := time.Now()
	elapsed := now.Sub(e.metrics.StartTime)

	// Update basic timing
	e.metrics.Duration = elapsed
	e.metrics.LastUpdated = now

	// Update state timing
	e.state.ElapsedTime = elapsed
	if e.testPlan.Duration.Duration > 0 {
		e.state.RemainingTime = e.testPlan.Duration.Duration - elapsed
		if e.state.RemainingTime < 0 {
			e.state.RemainingTime = 0
		}
		e.state.Progress = float64(elapsed) / float64(e.testPlan.Duration.Duration)
		if e.state.Progress > 1.0 {
			e.state.Progress = 1.0
		}
	}

	// Update worker metrics from worker pool
	if e.workerPool != nil {
		poolMetrics := e.workerPool.GetMetrics()
		e.metrics.ActiveWorkers = poolMetrics.ActiveWorkers
		e.metrics.TotalWorkers = int32(e.workerPool.GetWorkerCount())
		e.state.CurrentWorkers = poolMetrics.ActiveWorkers

		// Update request metrics
		e.metrics.RequestsExecuted = poolMetrics.TotalJobsProcessed
		e.metrics.RequestsSucceeded = poolMetrics.TotalJobsSuccessful
		e.metrics.RequestsFailed = poolMetrics.TotalJobsFailed

		// Calculate throughput
		if elapsed.Seconds() > 0 {
			e.metrics.Throughput = float64(e.metrics.RequestsExecuted) / elapsed.Seconds()
		}

		// Calculate error rate
		if e.metrics.RequestsExecuted > 0 {
			e.metrics.ErrorRate = float64(e.metrics.RequestsFailed) / float64(e.metrics.RequestsExecuted) * 100
		}

		// Update average latency from pool metrics
		e.metrics.AverageLatency = time.Duration(poolMetrics.AverageProcessingTime) * time.Millisecond
	}

	if e.config.EnableDebugLogs {
		log.Printf("Metrics: executed=%d, succeeded=%d, failed=%d, throughput=%.2f req/s, workers=%d/%d",
			e.metrics.RequestsExecuted, e.metrics.RequestsSucceeded, e.metrics.RequestsFailed,
			e.metrics.Throughput, e.metrics.ActiveWorkers, e.metrics.TotalWorkers)
	}
}
